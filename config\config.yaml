# Stock Analyzer Configuration

# Database Configuration
database:
  # For development, use SQLite
  type: sqlite  # sqlite or postgresql
  sqlite_path: ./data/stock_analyzer.db

  # PostgreSQL configuration (for production)
  host: localhost
  port: 5432
  name: stock_analyzer
  user: postgres
  password: ${DB_PASSWORD}
  pool_size: 10
  max_overflow: 20

# Data Sources Configuration
data_sources:
  nsepy:
    enabled: true
    rate_limit: 1  # requests per second
    timeout: 30

  yfinance:
    enabled: true
    rate_limit: 2
    timeout: 30

  # Add other data sources as needed
  bloomberg:
    enabled: false
    api_key: ${BLOOMBERG_API_KEY}

# Trading Strategies Configuration
strategies:
  rsi:
    enabled: true
    period: 14
    oversold_threshold: 30
    overbought_threshold: 70
    adaptive_volatility: true
    volatility_window: 20

  pivot_points:
    enabled: true
    volume_multiplier: 1.5
    support_resistance_levels: 3

  macd:
    enabled: true
    fast_period: 12
    slow_period: 26
    signal_period: 9
    ml_filter: true
    ml_model: RandomForest

  moving_averages:
    enabled: true
    short_period: 50
    long_period: 200
    adx_period: 14
    adx_threshold: 25

  breakout:
    enabled: true
    lookback_period: 20
    volume_multiplier: 2.0
    false_breakout_filter: true

# Backtesting Configuration
backtesting:
  initial_capital: 1000000  # 10 Lakhs
  commission: 0.001  # 0.1%
  slippage: 0.0005  # 0.05%
  position_size: 0.02  # 2% risk per trade
  max_positions: 10

  # Walk-forward analysis
  walk_forward:
    enabled: true
    train_period_months: 12
    test_period_months: 3
    step_months: 1

# Machine Learning Configuration
ml:
  reinforcement_learning:
    algorithm: PPO
    learning_rate: 0.0003
    batch_size: 64
    n_steps: 2048
    gamma: 0.99
    gae_lambda: 0.95
    clip_range: 0.2

  feature_engineering:
    technical_indicators: true
    price_patterns: true
    volume_analysis: true
    sentiment_analysis: false  # Future enhancement

# Risk Management
risk_management:
  max_portfolio_risk: 0.20  # 20%
  max_single_position: 0.05  # 5%
  stop_loss_percentage: 0.04  # 4%
  take_profit_percentage: 0.15  # 15%

  # Position sizing
  position_sizing_method: "kelly"  # kelly, fixed, volatility
  kelly_fraction: 0.25

# Alerts Configuration
alerts:
  email:
    enabled: false
    smtp_server: smtp.gmail.com
    smtp_port: 587
    username: ${EMAIL_USERNAME}
    password: ${EMAIL_PASSWORD}
    recipients: []

  sms:
    enabled: false
    twilio_account_sid: ${TWILIO_ACCOUNT_SID}
    twilio_auth_token: ${TWILIO_AUTH_TOKEN}
    from_number: ${TWILIO_FROM_NUMBER}
    to_numbers: []

# API Configuration
api:
  host: 0.0.0.0
  port: 8000
  debug: false
  cors_origins: ["http://localhost:3000", "http://localhost:8080"]

# Logging Configuration
logging:
  level: INFO
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  rotation: "1 day"
  retention: "30 days"

# Stock Universe Configuration
stock_universe:
  focus_index: "NIFTY50"  # Start with Nifty 50
  include_midcap: false
  include_smallcap: false
  min_market_cap: 1000  # Crores
  min_avg_volume: 100000  # Shares per day

# Performance Monitoring
monitoring:
  metrics_collection: true
  performance_tracking: true
  system_health_checks: true
  prometheus_port: 9090
