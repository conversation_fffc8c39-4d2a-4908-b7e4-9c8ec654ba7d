#!/usr/bin/env python3
"""
Initialize the database with tables and basic data.
"""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data.database import db_manager, Base, engine
from src.data.models import *  # Import all models
from src.utils.logger import get_logger

logger = get_logger(__name__)

def create_tables():
    """Create all database tables."""
    try:
        logger.info("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        return True
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        return False

def test_database():
    """Test database connection and basic operations."""
    try:
        logger.info("Testing database connection...")
        if not db_manager.test_connection():
            return False

        # Test basic operations
        with db_manager.get_session() as db:
            # Try to query a table (should be empty but should not error)
            from src.data.crud import StockCRUD
            stocks = StockCRUD.get_active_stocks(db)
            logger.info(f"Database test successful. Found {len(stocks)} stocks.")

        return True
    except Exception as e:
        logger.error(f"Database test failed: {e}")
        return False

def main():
    """Main initialization function."""
    logger.info("Starting database initialization...")

    # Create tables
    if not create_tables():
        logger.error("Failed to create tables")
        return False

    # Test database
    if not test_database():
        logger.error("Database test failed")
        return False

    logger.info("Database initialization completed successfully!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
