"""
FastAPI Main Application - Stock Analysis System API.
Provides REST endpoints for all system components.
"""

from fastapi import FastAP<PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
from contextlib import asynccontextmanager
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional

from .routers import (
    data_pipeline, strategies, backtesting, rl_optimizer,
    portfolio, alerts, auth, dashboard, live_trading, portfolio_optimization
)
from .websocket import websocket_manager
from .auth import auth_manager
from .models import APIResponse
from ..stock_ai_agent import stock_ai_agent
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger(__name__)

# Security
security = HTTPBearer()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    logger.info("Starting Stock Analysis API")

    try:
        # Initialize system components
        await initialize_system()

        # Start background tasks
        asyncio.create_task(background_tasks())

        logger.info("Stock Analysis API started successfully")
        yield

    except Exception as e:
        logger.error(f"Error during startup: {e}")
        raise
    finally:
        logger.info("Shutting down Stock Analysis API")
        await cleanup_system()

async def initialize_system():
    """Initialize all system components."""
    try:
        # Initialize database connections
        from ..data.database import db_manager
        if not db_manager.test_connection():
            raise Exception("Database connection failed")

        # Initialize WebSocket manager
        await websocket_manager.initialize()

        # Initialize authentication
        await auth_manager.initialize()

        logger.info("System components initialized")

    except Exception as e:
        logger.error(f"System initialization failed: {e}")
        raise

async def cleanup_system():
    """Cleanup system resources."""
    try:
        await websocket_manager.cleanup()
        logger.info("System cleanup completed")
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")

async def background_tasks():
    """Run background tasks."""
    try:
        # Start WebSocket manager
        await websocket_manager.start()

        # Start real-time data streaming
        asyncio.create_task(stream_real_time_data())

    except Exception as e:
        logger.error(f"Error in background tasks: {e}")

async def stream_real_time_data():
    """Stream real-time data to connected clients."""
    while True:
        try:
            # Get system status
            status = stock_ai_agent.get_system_status()

            # Broadcast to all connected clients
            await websocket_manager.broadcast({
                'type': 'system_status',
                'data': status,
                'timestamp': datetime.now().isoformat()
            })

            # Wait 30 seconds before next update
            await asyncio.sleep(30)

        except Exception as e:
            logger.error(f"Error streaming real-time data: {e}")
            await asyncio.sleep(60)  # Wait longer on error

# Create FastAPI application
app = FastAPI(
    title="Stock Analysis System API",
    description="Comprehensive AI-powered stock analysis and trading system",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(data_pipeline.router, prefix="/api/v1/data", tags=["Data Pipeline"])
app.include_router(strategies.router, prefix="/api/v1/strategies", tags=["Trading Strategies"])
app.include_router(backtesting.router, prefix="/api/v1/backtesting", tags=["Backtesting"])
app.include_router(rl_optimizer.router, prefix="/api/v1/rl", tags=["RL Optimizer"])
app.include_router(portfolio.router, prefix="/api/v1/portfolio", tags=["Portfolio"])
app.include_router(alerts.router, prefix="/api/v1/alerts", tags=["Alerts"])
app.include_router(dashboard.router, prefix="/api/v1/dashboard", tags=["Dashboard"])
app.include_router(live_trading.router, prefix="/api/v1/live-trading", tags=["Live Trading"])
app.include_router(portfolio_optimization.router, prefix="/api/v1/portfolio-optimization", tags=["Portfolio Optimization"])

# WebSocket endpoint
app.include_router(websocket_manager.router, prefix="/ws")

# Static files for dashboard
app.mount("/static", StaticFiles(directory="src/web/static"), name="static")

# Import authentication dependency
from .auth import get_current_user

# Root endpoint
@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve main dashboard."""
    try:
        with open("src/web/templates/dashboard.html", "r") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
            <head><title>Stock Analysis System</title></head>
            <body>
                <h1>Stock Analysis System API</h1>
                <p>API is running. Visit <a href="/docs">/docs</a> for API documentation.</p>
                <p>Dashboard will be available once frontend is deployed.</p>
            </body>
        </html>
        """)

# Health check endpoint
@app.get("/health", response_model=APIResponse)
async def health_check():
    """Health check endpoint."""
    try:
        system_status = stock_ai_agent.get_system_status()

        return APIResponse(
            success=True,
            message="System is healthy",
            data={
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "system_status": system_status
            }
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="System health check failed")

# System status endpoint
@app.get("/api/v1/system/status", response_model=APIResponse)
async def get_system_status(current_user: dict = Depends(get_current_user)):
    """Get comprehensive system status."""
    try:
        status = stock_ai_agent.get_system_status()

        return APIResponse(
            success=True,
            message="System status retrieved successfully",
            data=status
        )
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system status")

# System control endpoints
@app.post("/api/v1/system/start", response_model=APIResponse)
async def start_system(current_user: dict = Depends(get_current_user)):
    """Start the stock analysis system."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        # Start system in background
        asyncio.create_task(stock_ai_agent.run())

        return APIResponse(
            success=True,
            message="System start initiated",
            data={"status": "starting"}
        )
    except Exception as e:
        logger.error(f"Error starting system: {e}")
        raise HTTPException(status_code=500, detail="Failed to start system")

@app.post("/api/v1/system/stop", response_model=APIResponse)
async def stop_system(current_user: dict = Depends(get_current_user)):
    """Stop the stock analysis system."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        stock_ai_agent.stop()

        return APIResponse(
            success=True,
            message="System stop initiated",
            data={"status": "stopping"}
        )
    except Exception as e:
        logger.error(f"Error stopping system: {e}")
        raise HTTPException(status_code=500, detail="Failed to stop system")

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    """Handle 404 errors."""
    return APIResponse(
        success=False,
        message="Endpoint not found",
        data={"path": str(request.url.path)}
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    """Handle 500 errors."""
    logger.error(f"Internal server error: {exc}")
    return APIResponse(
        success=False,
        message="Internal server error",
        data={"error": str(exc)}
    )

# Development server
if __name__ == "__main__":
    uvicorn.run(
        "src.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
