"""
API Models - Pydantic models for request/response schemas.
"""

from pydantic import BaseModel, Field, validator
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, date
from enum import Enum

# Base response model
class APIResponse(BaseModel):
    """Standard API response format."""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.now)

# Authentication models
class LoginRequest(BaseModel):
    """Login request model."""
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=6)

class LoginResponse(BaseModel):
    """Login response model."""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user_info: Dict[str, Any]

class UserCreate(BaseModel):
    """User creation model."""
    username: str = Field(..., min_length=3, max_length=50)
    email: str = Field(..., pattern=r'^[^@]+@[^@]+\.[^@]+$')
    password: str = Field(..., min_length=6)
    full_name: Optional[str] = None
    is_admin: bool = False

class UserResponse(BaseModel):
    """User response model."""
    id: int
    username: str
    email: str
    full_name: Optional[str]
    is_admin: bool
    is_active: bool
    created_at: datetime

# Data Pipeline models
class DataUpdateRequest(BaseModel):
    """Data update request model."""
    symbols: Optional[List[str]] = None
    force_update: bool = False
    update_corporate_actions: bool = True

class DataQualityReport(BaseModel):
    """Data quality report model."""
    total_stocks: int
    updated_today: int
    data_quality_score: float
    missing_data_count: int
    last_update: datetime
    issues: List[Dict[str, Any]]

# Strategy models
class StrategyConfig(BaseModel):
    """Strategy configuration model."""
    strategy_name: str
    enabled: bool
    parameters: Dict[str, Any]

class StrategySignal(BaseModel):
    """Trading signal model."""
    id: Optional[int] = None
    symbol: str
    strategy_name: str
    signal_type: str  # BUY, SELL, HOLD
    signal_strength: float = Field(..., ge=0.0, le=1.0)
    price: float = Field(..., gt=0)
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    parameters: Dict[str, Any] = {}
    timestamp: datetime

class StrategyPerformance(BaseModel):
    """Strategy performance model."""
    strategy_name: str
    total_signals: int
    successful_signals: int
    win_rate: float
    avg_return: float
    sharpe_ratio: float
    max_drawdown: float
    last_signal: Optional[datetime]

# Backtesting models
class BacktestRequest(BaseModel):
    """Backtest request model."""
    strategy_name: str
    symbol: str
    start_date: date
    end_date: date
    initial_capital: float = Field(default=1000000, gt=0)
    parameters: Optional[Dict[str, Any]] = None

class BacktestResult(BaseModel):
    """Backtest result model."""
    strategy_name: str
    symbol: str
    start_date: date
    end_date: date
    initial_capital: float
    final_value: float
    total_return: float
    annual_return: float
    sharpe_ratio: float
    max_drawdown: float
    calmar_ratio: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float

class WalkForwardRequest(BaseModel):
    """Walk-forward analysis request."""
    strategy_name: str
    symbol: str
    start_date: date
    end_date: date
    train_months: int = Field(default=12, ge=6, le=24)
    test_months: int = Field(default=3, ge=1, le=6)
    step_months: int = Field(default=1, ge=1, le=3)

# RL Optimizer models
class RLTrainingRequest(BaseModel):
    """RL training request model."""
    strategy_name: Optional[str] = None  # None for all strategies
    num_iterations: int = Field(default=50, ge=10, le=200)
    episodes_per_iteration: int = Field(default=3, ge=1, le=10)
    symbols: Optional[List[str]] = None

class RLModelStatus(BaseModel):
    """RL model status model."""
    strategy_name: str
    model_type: str
    is_active: bool
    training_iterations: int
    avg_reward: float
    last_trained: Optional[datetime]
    performance_metrics: Dict[str, Any]

class ParameterOptimizationRequest(BaseModel):
    """Parameter optimization request."""
    strategy_name: str
    symbol: str
    parameter_ranges: Dict[str, List[Union[int, float]]]
    optimization_metric: str = "sharpe_ratio"

# Portfolio models
class Position(BaseModel):
    """Portfolio position model."""
    id: Optional[int] = None
    symbol: str
    quantity: int
    avg_price: float = Field(..., gt=0)
    current_price: Optional[float] = None
    market_value: Optional[float] = None
    unrealized_pnl: Optional[float] = None
    unrealized_pnl_percent: Optional[float] = None
    entry_date: datetime
    strategy_name: Optional[str] = None

class Trade(BaseModel):
    """Trade model."""
    id: Optional[int] = None
    symbol: str
    trade_type: str  # BUY, SELL
    quantity: int
    price: float = Field(..., gt=0)
    total_value: float
    commission: float = Field(default=0, ge=0)
    trade_date: datetime
    strategy_name: Optional[str] = None
    signal_id: Optional[int] = None

class PortfolioSummary(BaseModel):
    """Portfolio summary model."""
    total_value: float
    cash_balance: float
    invested_amount: float
    total_pnl: float
    total_pnl_percent: float
    day_pnl: float
    day_pnl_percent: float
    positions_count: int
    active_strategies: int

# Alert models
class AlertType(str, Enum):
    """Alert types."""
    SIGNAL = "signal"
    SYSTEM = "system"
    PERFORMANCE = "performance"
    ERROR = "error"

class AlertSeverity(str, Enum):
    """Alert severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class Alert(BaseModel):
    """Alert model."""
    id: Optional[int] = None
    alert_type: AlertType
    severity: AlertSeverity
    title: str
    message: str
    data: Optional[Dict[str, Any]] = None
    is_read: bool = False
    created_at: datetime
    expires_at: Optional[datetime] = None

class AlertSubscription(BaseModel):
    """Alert subscription model."""
    alert_types: List[AlertType]
    min_severity: AlertSeverity = AlertSeverity.MEDIUM
    email_notifications: bool = True
    push_notifications: bool = True

# Dashboard models
class DashboardData(BaseModel):
    """Dashboard data model."""
    system_status: Dict[str, Any]
    portfolio_summary: PortfolioSummary
    recent_signals: List[StrategySignal]
    strategy_performance: List[StrategyPerformance]
    market_overview: Dict[str, Any]
    alerts: List[Alert]
    rl_status: Dict[str, Any]

class MarketOverview(BaseModel):
    """Market overview model."""
    nifty50_change: float
    nifty50_change_percent: float
    market_status: str  # OPEN, CLOSED, PRE_OPEN
    top_gainers: List[Dict[str, Any]]
    top_losers: List[Dict[str, Any]]
    most_active: List[Dict[str, Any]]
    sector_performance: Dict[str, float]

# WebSocket models
class WebSocketMessage(BaseModel):
    """WebSocket message model."""
    type: str
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.now)

class SubscriptionRequest(BaseModel):
    """WebSocket subscription request."""
    channels: List[str]  # e.g., ['signals', 'portfolio', 'system_status']
    symbols: Optional[List[str]] = None

# Validation models
class DateRange(BaseModel):
    """Date range validation."""
    start_date: date
    end_date: date

    @validator('end_date')
    def end_date_must_be_after_start_date(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v

class PaginationParams(BaseModel):
    """Pagination parameters."""
    page: int = Field(default=1, ge=1)
    size: int = Field(default=20, ge=1, le=100)

    @property
    def offset(self) -> int:
        return (self.page - 1) * self.size

class PaginatedResponse(BaseModel):
    """Paginated response model."""
    items: List[Any]
    total: int
    page: int
    size: int
    pages: int

    @validator('pages', pre=True, always=True)
    def calculate_pages(cls, v, values):
        if 'total' in values and 'size' in values:
            return (values['total'] + values['size'] - 1) // values['size']
        return v

# Configuration models
class SystemConfig(BaseModel):
    """System configuration model."""
    data_update_frequency: str
    strategy_execution_frequency: str
    rl_training_enabled: bool
    rl_training_frequency: str
    alert_notifications_enabled: bool
    max_positions_per_strategy: int
    risk_management_enabled: bool

class StrategyParameterBounds(BaseModel):
    """Strategy parameter bounds model."""
    parameter_name: str
    min_value: Union[int, float]
    max_value: Union[int, float]
    data_type: str  # int, float, bool
    description: Optional[str] = None

# Error models
class ErrorDetail(BaseModel):
    """Error detail model."""
    code: str
    message: str
    field: Optional[str] = None

class ValidationError(BaseModel):
    """Validation error model."""
    detail: List[ErrorDetail]
