"""
Alerts Router - Handles alert management and notifications.
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
from datetime import datetime, timedelta

from ..models import APIResponse, Alert, AlertSubscription, AlertType, AlertSeverity, PaginationParams
from ..auth import get_current_user
from ..websocket import websocket_manager
from ...utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

@router.get("/", response_model=APIResponse)
async def get_alerts(
    pagination: PaginationParams = Depends(),
    alert_type: Optional[AlertType] = None,
    severity: Optional[AlertSeverity] = None,
    unread_only: bool = False,
    current_user: dict = Depends(get_current_user)
):
    """Get alerts."""
    try:
        # Mock alerts data
        alerts = [
            {
                'id': 1,
                'alert_type': 'signal',
                'severity': 'high',
                'title': 'Strong Buy Signal',
                'message': 'Adaptive RSI strategy generated strong buy signal for RELIANCE at ₹2520',
                'data': {'symbol': 'RELIANCE', 'strategy': 'adaptive_rsi', 'signal_strength': 0.85},
                'is_read': False,
                'created_at': '2024-01-25T10:30:00',
                'expires_at': None
            },
            {
                'id': 2,
                'alert_type': 'system',
                'severity': 'medium',
                'title': 'Data Update Completed',
                'message': 'Daily data update completed successfully for 50 stocks',
                'data': {'stocks_updated': 50, 'duration_minutes': 15},
                'is_read': True,
                'created_at': '2024-01-25T09:00:00',
                'expires_at': None
            },
            {
                'id': 3,
                'alert_type': 'performance',
                'severity': 'low',
                'title': 'Strategy Performance Update',
                'message': 'Enhanced Pivot strategy achieved 18% return this month',
                'data': {'strategy': 'enhanced_pivot', 'monthly_return': 18.0},
                'is_read': False,
                'created_at': '2024-01-25T08:00:00',
                'expires_at': None
            }
        ]

        # Apply filters
        filtered_alerts = alerts
        if alert_type:
            filtered_alerts = [a for a in filtered_alerts if a['alert_type'] == alert_type.value]
        if severity:
            filtered_alerts = [a for a in filtered_alerts if a['severity'] == severity.value]
        if unread_only:
            filtered_alerts = [a for a in filtered_alerts if not a['is_read']]

        return APIResponse(
            success=True,
            message="Alerts retrieved",
            data={
                "alerts": filtered_alerts,
                "total": len(filtered_alerts),
                "unread_count": len([a for a in alerts if not a['is_read']]),
                "page": pagination.page,
                "size": pagination.size
            }
        )

    except Exception as e:
        logger.error(f"Error getting alerts: {e}")
        raise HTTPException(status_code=500, detail="Failed to get alerts")

@router.post("/{alert_id}/mark-read", response_model=APIResponse)
async def mark_alert_read(
    alert_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Mark alert as read."""
    try:
        # In production, update alert in database
        return APIResponse(
            success=True,
            message="Alert marked as read",
            data={"alert_id": alert_id, "is_read": True}
        )

    except Exception as e:
        logger.error(f"Error marking alert as read: {e}")
        raise HTTPException(status_code=500, detail="Failed to mark alert as read")

@router.post("/mark-all-read", response_model=APIResponse)
async def mark_all_alerts_read(current_user: dict = Depends(get_current_user)):
    """Mark all alerts as read."""
    try:
        # In production, update all alerts in database
        return APIResponse(
            success=True,
            message="All alerts marked as read",
            data={"marked_count": 5}
        )

    except Exception as e:
        logger.error(f"Error marking all alerts as read: {e}")
        raise HTTPException(status_code=500, detail="Failed to mark all alerts as read")

@router.delete("/{alert_id}", response_model=APIResponse)
async def delete_alert(
    alert_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Delete alert."""
    try:
        return APIResponse(
            success=True,
            message="Alert deleted",
            data={"alert_id": alert_id}
        )

    except Exception as e:
        logger.error(f"Error deleting alert: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete alert")

@router.get("/subscription", response_model=APIResponse)
async def get_alert_subscription(current_user: dict = Depends(get_current_user)):
    """Get user's alert subscription preferences."""
    try:
        # Mock subscription data
        subscription = {
            'alert_types': ['signal', 'system', 'performance'],
            'min_severity': 'medium',
            'email_notifications': True,
            'push_notifications': True
        }

        return APIResponse(
            success=True,
            message="Alert subscription retrieved",
            data=subscription
        )

    except Exception as e:
        logger.error(f"Error getting alert subscription: {e}")
        raise HTTPException(status_code=500, detail="Failed to get alert subscription")

@router.post("/subscription", response_model=APIResponse)
async def update_alert_subscription(
    subscription: AlertSubscription,
    current_user: dict = Depends(get_current_user)
):
    """Update alert subscription preferences."""
    try:
        # In production, save to database
        return APIResponse(
            success=True,
            message="Alert subscription updated",
            data=subscription.dict()
        )

    except Exception as e:
        logger.error(f"Error updating alert subscription: {e}")
        raise HTTPException(status_code=500, detail="Failed to update alert subscription")

@router.post("/test", response_model=APIResponse)
async def send_test_alert(
    current_user: dict = Depends(get_current_user)
):
    """Send test alert."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        # Send test alert via WebSocket
        await websocket_manager.send_alert({
            'id': 999,
            'alert_type': 'system',
            'severity': 'low',
            'title': 'Test Alert',
            'message': 'This is a test alert to verify the notification system',
            'data': {'test': True},
            'is_read': False,
            'created_at': datetime.now().isoformat()
        })

        return APIResponse(
            success=True,
            message="Test alert sent",
            data={"test_alert_sent": True}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending test alert: {e}")
        raise HTTPException(status_code=500, detail="Failed to send test alert")
