"""
Backtesting Router - Handles backtesting operations and results.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import List, Optional, Dict, Any
from datetime import date, datetime

from ..models import (
    APIResponse, BacktestRequest, BacktestResult, WalkForwardRequest,
    PaginationParams
)
from ..auth import get_current_user
from ..websocket import websocket_manager
from ...backtesting.backtest_runner import backtest_runner
from ...backtesting.integration import backtesting_integration
from ...rl_optimizer.rl_integration import rl_integration
from ...data.database import db_manager
from ...data.crud import BacktestCRUD
from ...utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

@router.post("/run", response_model=APIResponse)
async def run_backtest(
    backtest_request: BacktestRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Run single strategy backtest."""
    try:
        # Validate date range
        if backtest_request.end_date <= backtest_request.start_date:
            raise HTTPException(status_code=400, detail="End date must be after start date")

        # Start backtest in background
        background_tasks.add_task(
            _run_single_backtest,
            backtest_request,
            current_user['id']
        )

        return APIResponse(
            success=True,
            message="Backtest initiated",
            data={
                "strategy_name": backtest_request.strategy_name,
                "symbol": backtest_request.symbol,
                "start_date": backtest_request.start_date.isoformat(),
                "end_date": backtest_request.end_date.isoformat(),
                "status": "running"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running backtest: {e}")
        raise HTTPException(status_code=500, detail="Failed to run backtest")

async def _run_single_backtest(request: BacktestRequest, user_id: int):
    """Run single backtest in background."""
    try:
        # Send start notification
        await websocket_manager.send_backtest_update({
            'status': 'started',
            'strategy_name': request.strategy_name,
            'symbol': request.symbol,
            'user_id': user_id
        })

        # Get RL-optimized parameters if not provided
        parameters = request.parameters or {}
        if not parameters:
            rl_params = rl_integration.get_optimized_parameters_for_strategy(
                request.strategy_name, request.symbol
            )
            if rl_params:
                parameters = rl_params

        # Run backtest
        result = backtest_runner.engine.run_single_strategy_backtest(
            request.strategy_name,
            request.symbol,
            request.start_date,
            request.end_date,
            parameters
        )

        if 'error' not in result:
            # Send success notification
            await websocket_manager.send_backtest_update({
                'status': 'completed',
                'strategy_name': request.strategy_name,
                'symbol': request.symbol,
                'result': result,
                'user_id': user_id
            })
        else:
            # Send error notification
            await websocket_manager.send_backtest_update({
                'status': 'failed',
                'strategy_name': request.strategy_name,
                'symbol': request.symbol,
                'error': result['error'],
                'user_id': user_id
            })

        logger.info(f"Backtest completed for {request.strategy_name} on {request.symbol}")

    except Exception as e:
        logger.error(f"Error in background backtest: {e}")
        await websocket_manager.send_backtest_update({
            'status': 'failed',
            'strategy_name': request.strategy_name,
            'symbol': request.symbol,
            'error': str(e),
            'user_id': user_id
        })

@router.post("/run-all", response_model=APIResponse)
async def run_all_strategies_backtest(
    background_tasks: BackgroundTasks,
    symbols: Optional[List[str]] = None,
    current_user: dict = Depends(get_current_user)
):
    """Run backtest for all strategies."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        # Start comprehensive backtest in background
        background_tasks.add_task(
            _run_comprehensive_backtest,
            symbols,
            current_user['id']
        )

        return APIResponse(
            success=True,
            message="Comprehensive backtest initiated",
            data={
                "symbols": symbols or "all_nifty50",
                "status": "running"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running comprehensive backtest: {e}")
        raise HTTPException(status_code=500, detail="Failed to run comprehensive backtest")

async def _run_comprehensive_backtest(symbols: Optional[List[str]], user_id: int):
    """Run comprehensive backtest in background."""
    try:
        # Send start notification
        await websocket_manager.send_backtest_update({
            'status': 'started',
            'type': 'comprehensive',
            'user_id': user_id
        })

        # Run comprehensive backtest
        if symbols:
            # Custom symbol list
            results = {}
            for symbol in symbols:
                symbol_results = backtest_runner.run_all_strategies_backtest([symbol])
                results[symbol] = symbol_results
        else:
            # All Nifty 50 stocks
            results = backtest_runner.run_all_strategies_backtest()

        # Send completion notification
        await websocket_manager.send_backtest_update({
            'status': 'completed',
            'type': 'comprehensive',
            'results': results,
            'user_id': user_id
        })

        logger.info("Comprehensive backtest completed")

    except Exception as e:
        logger.error(f"Error in comprehensive backtest: {e}")
        await websocket_manager.send_backtest_update({
            'status': 'failed',
            'type': 'comprehensive',
            'error': str(e),
            'user_id': user_id
        })

@router.post("/walk-forward", response_model=APIResponse)
async def run_walk_forward_analysis(
    wf_request: WalkForwardRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Run walk-forward analysis."""
    try:
        # Validate parameters
        if wf_request.end_date <= wf_request.start_date:
            raise HTTPException(status_code=400, detail="End date must be after start date")

        if wf_request.train_months < wf_request.test_months:
            raise HTTPException(status_code=400, detail="Training period must be longer than test period")

        # Start walk-forward analysis in background
        background_tasks.add_task(
            _run_walk_forward_analysis,
            wf_request,
            current_user['id']
        )

        return APIResponse(
            success=True,
            message="Walk-forward analysis initiated",
            data={
                "strategy_name": wf_request.strategy_name,
                "symbol": wf_request.symbol,
                "train_months": wf_request.train_months,
                "test_months": wf_request.test_months,
                "status": "running"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running walk-forward analysis: {e}")
        raise HTTPException(status_code=500, detail="Failed to run walk-forward analysis")

async def _run_walk_forward_analysis(request: WalkForwardRequest, user_id: int):
    """Run walk-forward analysis in background."""
    try:
        # Send start notification
        await websocket_manager.send_backtest_update({
            'status': 'started',
            'type': 'walk_forward',
            'strategy_name': request.strategy_name,
            'symbol': request.symbol,
            'user_id': user_id
        })

        # Run walk-forward analysis
        results = backtest_runner.run_walk_forward_validation(
            request.strategy_name,
            [request.symbol],
            train_months=request.train_months,
            test_months=request.test_months,
            step_months=request.step_months
        )

        # Send completion notification
        await websocket_manager.send_backtest_update({
            'status': 'completed',
            'type': 'walk_forward',
            'strategy_name': request.strategy_name,
            'symbol': request.symbol,
            'results': results,
            'user_id': user_id
        })

        logger.info(f"Walk-forward analysis completed for {request.strategy_name}")

    except Exception as e:
        logger.error(f"Error in walk-forward analysis: {e}")
        await websocket_manager.send_backtest_update({
            'status': 'failed',
            'type': 'walk_forward',
            'strategy_name': request.strategy_name,
            'symbol': request.symbol,
            'error': str(e),
            'user_id': user_id
        })

@router.get("/results", response_model=APIResponse)
async def get_backtest_results(
    pagination: PaginationParams = Depends(),
    strategy_name: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get backtest results."""
    try:
        with db_manager.get_session() as db:
            if strategy_name:
                results = BacktestCRUD.get_strategy_results(
                    db, strategy_name, limit=pagination.size
                )
            else:
                # Get all results with pagination
                query = db.query(BacktestResult).order_by(
                    BacktestResult.created_at.desc()
                )

                total = query.count()
                results = query.offset(pagination.offset).limit(pagination.size).all()

            results_data = []
            for result in results:
                result_dict = {
                    'id': result.id,
                    'strategy_name': result.strategy_name,
                    'start_date': result.start_date.isoformat(),
                    'end_date': result.end_date.isoformat(),
                    'initial_capital': float(result.initial_capital),
                    'total_return': float(result.total_return),
                    'annual_return': float(result.annual_return),
                    'sharpe_ratio': float(result.sharpe_ratio),
                    'max_drawdown': float(result.max_drawdown),
                    'calmar_ratio': float(result.calmar_ratio),
                    'total_trades': result.total_trades,
                    'winning_trades': result.winning_trades,
                    'losing_trades': result.losing_trades,
                    'win_rate': float(result.win_rate),
                    'avg_win': float(result.avg_win),
                    'avg_loss': float(result.avg_loss),
                    'profit_factor': float(result.profit_factor),
                    'created_at': result.created_at.isoformat()
                }
                results_data.append(result_dict)

            return APIResponse(
                success=True,
                message="Backtest results retrieved",
                data={
                    "results": results_data,
                    "total": len(results_data) if strategy_name else total,
                    "page": pagination.page,
                    "size": pagination.size
                }
            )

    except Exception as e:
        logger.error(f"Error getting backtest results: {e}")
        raise HTTPException(status_code=500, detail="Failed to get backtest results")

@router.get("/results/{result_id}", response_model=APIResponse)
async def get_backtest_result_details(
    result_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get detailed backtest result."""
    try:
        with db_manager.get_session() as db:
            result = db.query(BacktestResult).filter(
                BacktestResult.id == result_id
            ).first()

            if not result:
                raise HTTPException(status_code=404, detail="Backtest result not found")

            result_data = {
                'id': result.id,
                'strategy_name': result.strategy_name,
                'start_date': result.start_date.isoformat(),
                'end_date': result.end_date.isoformat(),
                'initial_capital': float(result.initial_capital),
                'total_return': float(result.total_return),
                'annual_return': float(result.annual_return),
                'sharpe_ratio': float(result.sharpe_ratio),
                'max_drawdown': float(result.max_drawdown),
                'calmar_ratio': float(result.calmar_ratio),
                'total_trades': result.total_trades,
                'winning_trades': result.winning_trades,
                'losing_trades': result.losing_trades,
                'win_rate': float(result.win_rate),
                'avg_win': float(result.avg_win),
                'avg_loss': float(result.avg_loss),
                'profit_factor': float(result.profit_factor),
                'parameters': result.parameters,
                'created_at': result.created_at.isoformat()
            }

            return APIResponse(
                success=True,
                message="Backtest result details retrieved",
                data=result_data
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting backtest result details: {e}")
        raise HTTPException(status_code=500, detail="Failed to get backtest result details")

@router.get("/performance-comparison", response_model=APIResponse)
async def get_performance_comparison(
    strategies: List[str],
    symbol: str,
    current_user: dict = Depends(get_current_user)
):
    """Compare performance of multiple strategies."""
    try:
        if len(strategies) < 2:
            raise HTTPException(status_code=400, detail="At least 2 strategies required for comparison")

        # Run comparison
        comparison_results = backtest_runner.run_strategy_comparison(strategies, symbol)

        return APIResponse(
            success=True,
            message="Strategy performance comparison completed",
            data={
                "symbol": symbol,
                "strategies": strategies,
                "comparison_results": comparison_results
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in performance comparison: {e}")
        raise HTTPException(status_code=500, detail="Failed to compare strategy performance")

@router.get("/best-performers", response_model=APIResponse)
async def get_best_performing_strategies(
    metric: str = "sharpe_ratio",
    limit: int = 10,
    current_user: dict = Depends(get_current_user)
):
    """Get best performing strategies."""
    try:
        valid_metrics = ['sharpe_ratio', 'total_return', 'calmar_ratio', 'win_rate']
        if metric not in valid_metrics:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid metric. Must be one of: {valid_metrics}"
            )

        with db_manager.get_session() as db:
            best_performers = BacktestCRUD.get_best_performing_strategies(db, metric)

            performers_data = []
            for result in best_performers[:limit]:
                performer_dict = {
                    'strategy_name': result.strategy_name,
                    'metric_value': float(getattr(result, metric)),
                    'total_return': float(result.total_return),
                    'sharpe_ratio': float(result.sharpe_ratio),
                    'max_drawdown': float(result.max_drawdown),
                    'total_trades': result.total_trades,
                    'win_rate': float(result.win_rate),
                    'created_at': result.created_at.isoformat()
                }
                performers_data.append(performer_dict)

            return APIResponse(
                success=True,
                message="Best performing strategies retrieved",
                data={
                    "metric": metric,
                    "performers": performers_data,
                    "count": len(performers_data)
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting best performers: {e}")
        raise HTTPException(status_code=500, detail="Failed to get best performing strategies")

@router.delete("/results/{result_id}", response_model=APIResponse)
async def delete_backtest_result(
    result_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Delete backtest result."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        with db_manager.get_session() as db:
            result = db.query(BacktestResult).filter(
                BacktestResult.id == result_id
            ).first()

            if not result:
                raise HTTPException(status_code=404, detail="Backtest result not found")

            db.delete(result)
            db.commit()

            return APIResponse(
                success=True,
                message="Backtest result deleted",
                data={"result_id": result_id}
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting backtest result: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete backtest result")
