"""
Dashboard Router - Provides dashboard data and analytics.
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
from datetime import datetime, date, timedelta

from ..models import APIResponse, DashboardData, MarketOverview
from ..auth import get_current_user
from ...stock_ai_agent import stock_ai_agent
from ...data.database import db_manager
from ...data.crud import StockCRUD, SignalCRUD
from ...utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

@router.get("/", response_model=APIResponse)
async def get_dashboard_data(current_user: dict = Depends(get_current_user)):
    """Get comprehensive dashboard data."""
    try:
        # Get system status
        system_status = stock_ai_agent.get_system_status()

        # Get portfolio summary (mock data)
        portfolio_summary = {
            'total_value': 1250000.0,
            'cash_balance': 150000.0,
            'invested_amount': 1100000.0,
            'total_pnl': 250000.0,
            'total_pnl_percent': 25.0,
            'day_pnl': 15000.0,
            'day_pnl_percent': 1.2,
            'positions_count': 8,
            'active_strategies': 5
        }

        # Get recent signals
        with db_manager.get_session() as db:
            recent_signals = SignalCRUD.get_latest_signals(db, limit=10)

        signals_data = []
        for signal in recent_signals:
            signal_dict = {
                'id': signal.id,
                'symbol': signal.symbol,
                'strategy_name': signal.strategy_name,
                'signal_type': signal.signal_type,
                'signal_strength': float(signal.signal_strength),
                'price': float(signal.price),
                'confidence_score': float(signal.confidence_score),
                'signal_time': signal.signal_time.isoformat()
            }
            signals_data.append(signal_dict)

        # Get strategy performance (mock data)
        strategy_performance = [
            {
                'strategy_name': 'adaptive_rsi',
                'total_signals': 45,
                'successful_signals': 32,
                'win_rate': 71.1,
                'avg_return': 2.8,
                'sharpe_ratio': 1.6,
                'max_drawdown': 8.2,
                'last_signal': '2024-01-25T10:30:00'
            },
            {
                'strategy_name': 'enhanced_pivot',
                'total_signals': 38,
                'successful_signals': 28,
                'win_rate': 73.7,
                'avg_return': 3.2,
                'sharpe_ratio': 1.8,
                'max_drawdown': 6.5,
                'last_signal': '2024-01-25T09:15:00'
            }
        ]

        # Get market overview (mock data)
        market_overview = {
            'nifty50_change': 125.80,
            'nifty50_change_percent': 0.68,
            'market_status': 'OPEN',
            'top_gainers': [
                {'symbol': 'RELIANCE', 'change_percent': 2.5},
                {'symbol': 'TCS', 'change_percent': 1.8},
                {'symbol': 'INFY', 'change_percent': 1.6}
            ],
            'top_losers': [
                {'symbol': 'HDFC', 'change_percent': -1.2},
                {'symbol': 'ICICIBANK', 'change_percent': -0.8}
            ],
            'most_active': [
                {'symbol': 'RELIANCE', 'volume': 5000000},
                {'symbol': 'TCS', 'volume': 3500000}
            ],
            'sector_performance': {
                'IT': 1.2,
                'Banking': -0.5,
                'Energy': 0.8,
                'Pharma': 0.3
            }
        }

        # Get alerts (mock data)
        alerts = [
            {
                'id': 1,
                'alert_type': 'signal',
                'severity': 'high',
                'title': 'Strong Buy Signal',
                'message': 'Adaptive RSI strategy generated strong buy signal for RELIANCE',
                'is_read': False,
                'created_at': '2024-01-25T10:30:00'
            }
        ]

        # Get RL status
        from ...rl_optimizer.rl_integration import rl_integration
        rl_status = rl_integration.get_rl_system_status()

        dashboard_data = {
            'system_status': system_status,
            'portfolio_summary': portfolio_summary,
            'recent_signals': signals_data,
            'strategy_performance': strategy_performance,
            'market_overview': market_overview,
            'alerts': alerts,
            'rl_status': rl_status
        }

        return APIResponse(
            success=True,
            message="Dashboard data retrieved",
            data=dashboard_data
        )

    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}")
        raise HTTPException(status_code=500, detail="Failed to get dashboard data")

@router.get("/market-overview", response_model=APIResponse)
async def get_market_overview(current_user: dict = Depends(get_current_user)):
    """Get market overview data."""
    try:
        # Mock market data - in production, this would come from market data provider
        market_overview = {
            'nifty50_change': 125.80,
            'nifty50_change_percent': 0.68,
            'market_status': 'OPEN',
            'top_gainers': [
                {'symbol': 'RELIANCE', 'price': 2520.0, 'change': 62.5, 'change_percent': 2.5},
                {'symbol': 'TCS', 'price': 3950.0, 'change': 70.0, 'change_percent': 1.8},
                {'symbol': 'INFY', 'price': 1680.0, 'change': 26.5, 'change_percent': 1.6},
                {'symbol': 'HDFCBANK', 'price': 1580.0, 'change': 22.0, 'change_percent': 1.4},
                {'symbol': 'ICICIBANK', 'price': 980.0, 'change': 12.5, 'change_percent': 1.3}
            ],
            'top_losers': [
                {'symbol': 'HDFC', 'price': 2650.0, 'change': -32.0, 'change_percent': -1.2},
                {'symbol': 'KOTAKBANK', 'price': 1720.0, 'change': -15.5, 'change_percent': -0.9},
                {'symbol': 'AXISBANK', 'price': 1050.0, 'change': -8.5, 'change_percent': -0.8},
                {'symbol': 'SBIN', 'price': 620.0, 'change': -4.2, 'change_percent': -0.7},
                {'symbol': 'BAJFINANCE', 'price': 6800.0, 'change': -35.0, 'change_percent': -0.5}
            ],
            'most_active': [
                {'symbol': 'RELIANCE', 'volume': 5000000, 'value': ***********},
                {'symbol': 'TCS', 'volume': 3500000, 'value': ***********},
                {'symbol': 'INFY', 'volume': 4200000, 'value': **********},
                {'symbol': 'HDFCBANK', 'volume': 2800000, 'value': **********},
                {'symbol': 'ICICIBANK', 'volume': 3100000, 'value': **********}
            ],
            'sector_performance': {
                'IT': 1.2,
                'Banking': -0.5,
                'Energy': 0.8,
                'Pharma': 0.3,
                'Auto': -0.2,
                'FMCG': 0.1,
                'Metals': 0.6,
                'Realty': -0.4
            }
        }

        return APIResponse(
            success=True,
            message="Market overview retrieved",
            data=market_overview
        )

    except Exception as e:
        logger.error(f"Error getting market overview: {e}")
        raise HTTPException(status_code=500, detail="Failed to get market overview")

@router.get("/analytics", response_model=APIResponse)
async def get_analytics_data(
    period: str = "30d",
    current_user: dict = Depends(get_current_user)
):
    """Get analytics data for dashboard charts."""
    try:
        # Parse period
        if period == "7d":
            days = 7
        elif period == "30d":
            days = 30
        elif period == "90d":
            days = 90
        else:
            days = 30

        # Mock analytics data
        analytics = {
            'period': period,
            'portfolio_performance': {
                'dates': [(date.today() - timedelta(days=i)).isoformat() for i in range(days, 0, -1)],
                'values': [1000000 + i * 1000 + (i % 5) * 5000 for i in range(days)],
                'returns': [(i % 10) * 0.5 - 2 for i in range(days)]
            },
            'strategy_returns': {
                'adaptive_rsi': [2.5, 3.1, 2.8, 3.5, 2.9],
                'enhanced_pivot': [3.2, 2.8, 3.6, 3.1, 3.4],
                'ml_enhanced_macd': [1.8, 2.2, 2.0, 2.5, 2.1],
                'dynamic_moving_averages': [2.1, 2.6, 2.3, 2.8, 2.4],
                'volume_confirmed_breakout': [2.9, 3.3, 3.0, 3.7, 3.2]
            },
            'signal_distribution': {
                'BUY': 65,
                'SELL': 35,
                'HOLD': 0
            },
            'sector_allocation': {
                'IT': 25.0,
                'Banking': 20.0,
                'Energy': 15.0,
                'Pharma': 12.0,
                'Auto': 10.0,
                'FMCG': 8.0,
                'Others': 10.0
            },
            'risk_metrics': {
                'portfolio_beta': 1.15,
                'sharpe_ratio': 1.8,
                'sortino_ratio': 2.1,
                'max_drawdown': 8.2,
                'var_95': -2.5,
                'volatility': 15.6
            }
        }

        return APIResponse(
            success=True,
            message="Analytics data retrieved",
            data=analytics
        )

    except Exception as e:
        logger.error(f"Error getting analytics data: {e}")
        raise HTTPException(status_code=500, detail="Failed to get analytics data")

@router.get("/system-health", response_model=APIResponse)
async def get_system_health(current_user: dict = Depends(get_current_user)):
    """Get system health metrics."""
    try:
        # Get comprehensive system health
        system_status = stock_ai_agent.get_system_status()

        # Add additional health metrics
        health_metrics = {
            'overall_status': 'healthy',
            'uptime_hours': 168.5,
            'cpu_usage': 45.2,
            'memory_usage': 62.8,
            'disk_usage': 35.1,
            'database_connections': 8,
            'api_response_time_ms': 125,
            'websocket_connections': 15,
            'last_data_update': '2024-01-25T09:00:00',
            'system_status': system_status
        }

        return APIResponse(
            success=True,
            message="System health retrieved",
            data=health_metrics
        )

    except Exception as e:
        logger.error(f"Error getting system health: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system health")
