"""
Data Pipeline Router - Handles data pipeline operations and monitoring.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import List, Optional
from datetime import date, datetime, timedelta

from ..models import APIResponse, DataUpdateRequest, DataQualityReport, PaginationParams
from ..auth import get_current_user
from ...data.data_pipeline import data_pipeline
from ...data.data_monitor import alert_manager
from ...data.database import db_manager
from ...data.crud import StockCRUD, PriceCRUD
from ...utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

@router.get("/status", response_model=APIResponse)
async def get_data_pipeline_status(current_user: dict = Depends(get_current_user)):
    """Get data pipeline status."""
    try:
        status = data_pipeline.get_pipeline_status()

        return APIResponse(
            success=True,
            message="Data pipeline status retrieved",
            data=status
        )

    except Exception as e:
        logger.error(f"Error getting data pipeline status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get pipeline status")

@router.get("/quality-report", response_model=APIResponse)
async def get_data_quality_report(current_user: dict = Depends(get_current_user)):
    """Get data quality report."""
    try:
        report = data_pipeline.get_data_quality_report()

        return APIResponse(
            success=True,
            message="Data quality report retrieved",
            data=report
        )

    except Exception as e:
        logger.error(f"Error getting data quality report: {e}")
        raise HTTPException(status_code=500, detail="Failed to get quality report")

@router.post("/update", response_model=APIResponse)
async def trigger_data_update(
    update_request: DataUpdateRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Trigger data update."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        # Start data update in background
        background_tasks.add_task(
            _run_data_update,
            update_request.symbols,
            update_request.force_update,
            update_request.update_corporate_actions
        )

        return APIResponse(
            success=True,
            message="Data update initiated",
            data={
                "symbols": update_request.symbols,
                "force_update": update_request.force_update,
                "update_corporate_actions": update_request.update_corporate_actions
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error triggering data update: {e}")
        raise HTTPException(status_code=500, detail="Failed to trigger data update")

async def _run_data_update(symbols: Optional[List[str]], force_update: bool, update_corporate_actions: bool):
    """Run data update in background."""
    try:
        if symbols:
            # Update specific symbols
            for symbol in symbols:
                await data_pipeline.update_stock_data(symbol, force_update)
        else:
            # Update all active stocks
            await data_pipeline.run_daily_update()

        if update_corporate_actions:
            await data_pipeline.update_corporate_actions()

        logger.info("Background data update completed")

    except Exception as e:
        logger.error(f"Error in background data update: {e}")

@router.get("/stocks", response_model=APIResponse)
async def get_stocks(
    pagination: PaginationParams = Depends(),
    active_only: bool = True,
    nifty50_only: bool = False,
    current_user: dict = Depends(get_current_user)
):
    """Get stocks list."""
    try:
        with db_manager.get_session() as db:
            if nifty50_only:
                stocks = StockCRUD.get_nifty50_stocks(db)
            elif active_only:
                stocks = StockCRUD.get_active_stocks(db)
            else:
                # Get all stocks with pagination
                stocks = db.query(StockMetadata).offset(pagination.offset).limit(pagination.size).all()

            # Convert to dict format
            stocks_data = []
            for stock in stocks:
                stock_dict = {
                    'id': stock.id,
                    'symbol': stock.symbol,
                    'company_name': stock.company_name,
                    'sector': stock.sector,
                    'industry': stock.industry,
                    'market_cap': float(stock.market_cap) if stock.market_cap else None,
                    'is_nifty50': stock.is_nifty50,
                    'is_active': stock.is_active,
                    'last_updated': stock.last_updated.isoformat() if stock.last_updated else None
                }
                stocks_data.append(stock_dict)

            return APIResponse(
                success=True,
                message="Stocks retrieved successfully",
                data={
                    "stocks": stocks_data,
                    "total": len(stocks_data),
                    "page": pagination.page,
                    "size": pagination.size
                }
            )

    except Exception as e:
        logger.error(f"Error getting stocks: {e}")
        raise HTTPException(status_code=500, detail="Failed to get stocks")

@router.get("/stocks/{symbol}", response_model=APIResponse)
async def get_stock_details(symbol: str, current_user: dict = Depends(get_current_user)):
    """Get detailed stock information."""
    try:
        with db_manager.get_session() as db:
            stock = StockCRUD.get_stock_by_symbol(db, symbol)

            if not stock:
                raise HTTPException(status_code=404, detail="Stock not found")

            # Get latest price
            latest_price = PriceCRUD.get_latest_price(db, stock.id)

            stock_data = {
                'id': stock.id,
                'symbol': stock.symbol,
                'company_name': stock.company_name,
                'sector': stock.sector,
                'industry': stock.industry,
                'market_cap': float(stock.market_cap) if stock.market_cap else None,
                'is_nifty50': stock.is_nifty50,
                'is_active': stock.is_active,
                'last_updated': stock.last_updated.isoformat() if stock.last_updated else None,
                'latest_price': {
                    'date': latest_price.date.isoformat() if latest_price else None,
                    'open': float(latest_price.open_price) if latest_price else None,
                    'high': float(latest_price.high_price) if latest_price else None,
                    'low': float(latest_price.low_price) if latest_price else None,
                    'close': float(latest_price.close_price) if latest_price else None,
                    'volume': latest_price.volume if latest_price else None
                } if latest_price else None
            }

            return APIResponse(
                success=True,
                message="Stock details retrieved",
                data=stock_data
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting stock details: {e}")
        raise HTTPException(status_code=500, detail="Failed to get stock details")

@router.get("/stocks/{symbol}/prices", response_model=APIResponse)
async def get_stock_prices(
    symbol: str,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get stock price history."""
    try:
        with db_manager.get_session() as db:
            stock = StockCRUD.get_stock_by_symbol(db, symbol)

            if not stock:
                raise HTTPException(status_code=404, detail="Stock not found")

            # Default date range (last 30 days)
            if not start_date:
                start_date = date.today() - timedelta(days=30)
            if not end_date:
                end_date = date.today()

            prices = PriceCRUD.get_price_history(db, stock.id, start_date, end_date)

            prices_data = []
            for price in prices:
                price_dict = {
                    'date': price.date.isoformat(),
                    'open': float(price.open_price),
                    'high': float(price.high_price),
                    'low': float(price.low_price),
                    'close': float(price.close_price),
                    'volume': price.volume,
                    'sma_20': float(price.sma_20) if price.sma_20 else None,
                    'sma_50': float(price.sma_50) if price.sma_50 else None,
                    'rsi_14': float(price.rsi_14) if price.rsi_14 else None,
                    'macd': float(price.macd) if price.macd else None,
                    'macd_signal': float(price.macd_signal) if price.macd_signal else None
                }
                prices_data.append(price_dict)

            return APIResponse(
                success=True,
                message="Price history retrieved",
                data={
                    "symbol": symbol,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "prices": prices_data,
                    "count": len(prices_data)
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting stock prices: {e}")
        raise HTTPException(status_code=500, detail="Failed to get stock prices")

@router.get("/alerts", response_model=APIResponse)
async def get_data_alerts(
    pagination: PaginationParams = Depends(),
    severity: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get data pipeline alerts."""
    try:
        alerts = alert_manager.get_recent_alerts(
            limit=pagination.size,
            offset=pagination.offset,
            severity=severity
        )

        return APIResponse(
            success=True,
            message="Data alerts retrieved",
            data={
                "alerts": alerts,
                "page": pagination.page,
                "size": pagination.size
            }
        )

    except Exception as e:
        logger.error(f"Error getting data alerts: {e}")
        raise HTTPException(status_code=500, detail="Failed to get data alerts")

@router.post("/validate", response_model=APIResponse)
async def validate_data(
    symbol: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    current_user: dict = Depends(get_current_user)
):
    """Validate data integrity."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        validation_result = await data_pipeline.validate_data_integrity(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date
        )

        return APIResponse(
            success=True,
            message="Data validation completed",
            data=validation_result
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating data: {e}")
        raise HTTPException(status_code=500, detail="Failed to validate data")

@router.post("/cleanup", response_model=APIResponse)
async def cleanup_data(
    days_to_keep: int = 365,
    current_user: dict = Depends(get_current_user)
):
    """Cleanup old data."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        if days_to_keep < 30:
            raise HTTPException(status_code=400, detail="Must keep at least 30 days of data")

        cleanup_result = await data_pipeline.cleanup_old_data(days_to_keep)

        return APIResponse(
            success=True,
            message="Data cleanup completed",
            data=cleanup_result
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cleaning up data: {e}")
        raise HTTPException(status_code=500, detail="Failed to cleanup data")

@router.get("/statistics", response_model=APIResponse)
async def get_data_statistics(current_user: dict = Depends(get_current_user)):
    """Get data statistics."""
    try:
        with db_manager.get_session() as db:
            # Get basic statistics
            total_stocks = db.execute("SELECT COUNT(*) FROM stock_metadata WHERE is_active = TRUE").fetchone()[0]
            nifty50_stocks = db.execute("SELECT COUNT(*) FROM stock_metadata WHERE is_nifty50 = TRUE AND is_active = TRUE").fetchone()[0]

            # Get price data statistics
            latest_date = db.execute("SELECT MAX(date) FROM daily_prices").fetchone()[0]
            total_price_records = db.execute("SELECT COUNT(*) FROM daily_prices").fetchone()[0]

            # Get data freshness
            today = date.today()
            updated_today = db.execute(
                "SELECT COUNT(DISTINCT stock_id) FROM daily_prices WHERE date = %s",
                (today,)
            ).fetchone()[0]

            statistics = {
                'total_active_stocks': total_stocks,
                'nifty50_stocks': nifty50_stocks,
                'total_price_records': total_price_records,
                'latest_data_date': latest_date.isoformat() if latest_date else None,
                'stocks_updated_today': updated_today,
                'data_freshness_percent': (updated_today / total_stocks * 100) if total_stocks > 0 else 0
            }

            return APIResponse(
                success=True,
                message="Data statistics retrieved",
                data=statistics
            )

    except Exception as e:
        logger.error(f"Error getting data statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get data statistics")
