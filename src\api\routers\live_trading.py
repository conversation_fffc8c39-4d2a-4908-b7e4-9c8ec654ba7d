"""
Live Trading Router - API endpoints for live trading operations.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..models import APIResponse, PaginationParams
from ..auth import get_current_user
from ..websocket import websocket_manager
from ...live_trading.integration import (
    live_trading_integration, initialize_live_trading,
    start_auto_trading, stop_auto_trading
)
from ...live_trading.trading_engine import TradingMode
from ...live_trading.broker_integration import get_broker
from ...live_trading.order_manager import order_manager, OrderRequest, OrderSide, OrderType, ProductType
from ...live_trading.risk_manager import risk_manager
from ...utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

@router.get("/status", response_model=APIResponse)
async def get_live_trading_status(current_user: dict = Depends(get_current_user)):
    """Get live trading system status."""
    try:
        status = await live_trading_integration.get_system_status()

        return APIResponse(
            success=True,
            message="Live trading status retrieved",
            data=status
        )

    except Exception as e:
        logger.error(f"Error getting live trading status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get live trading status")

@router.post("/initialize", response_model=APIResponse)
async def initialize_live_trading_system(
    current_user: dict = Depends(get_current_user)
):
    """Initialize live trading system."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        success = await initialize_live_trading()

        if success:
            return APIResponse(
                success=True,
                message="Live trading system initialized successfully",
                data={"initialized": True}
            )
        else:
            return APIResponse(
                success=False,
                message="Failed to initialize live trading system",
                data={"initialized": False}
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error initializing live trading: {e}")
        raise HTTPException(status_code=500, detail="Failed to initialize live trading")

@router.post("/start", response_model=APIResponse)
async def start_live_trading(
    mode: str = "PAPER",
    current_user: dict = Depends(get_current_user)
):
    """Start live trading."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        # Validate mode
        try:
            trading_mode = TradingMode(mode.upper())
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid trading mode: {mode}")

        # Start auto trading
        result = await start_auto_trading(trading_mode)

        if result['success']:
            # Send WebSocket notification
            await websocket_manager.broadcast({
                'type': 'live_trading_started',
                'data': result
            }, 'system_status')

            return APIResponse(
                success=True,
                message=result['message'],
                data=result
            )
        else:
            return APIResponse(
                success=False,
                message=result['message'],
                data=result
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting live trading: {e}")
        raise HTTPException(status_code=500, detail="Failed to start live trading")

@router.post("/stop", response_model=APIResponse)
async def stop_live_trading(current_user: dict = Depends(get_current_user)):
    """Stop live trading."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        result = await stop_auto_trading()

        if result['success']:
            # Send WebSocket notification
            await websocket_manager.broadcast({
                'type': 'live_trading_stopped',
                'data': result
            }, 'system_status')

            return APIResponse(
                success=True,
                message=result['message'],
                data=result
            )
        else:
            return APIResponse(
                success=False,
                message=result['message'],
                data=result
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping live trading: {e}")
        raise HTTPException(status_code=500, detail="Failed to stop live trading")

@router.get("/portfolio", response_model=APIResponse)
async def get_live_portfolio(current_user: dict = Depends(get_current_user)):
    """Get live portfolio status."""
    try:
        portfolio_status = await live_trading_integration.get_live_portfolio_status()

        return APIResponse(
            success=True,
            message="Live portfolio status retrieved",
            data=portfolio_status
        )

    except Exception as e:
        logger.error(f"Error getting live portfolio: {e}")
        raise HTTPException(status_code=500, detail="Failed to get live portfolio")

@router.get("/positions", response_model=APIResponse)
async def get_live_positions(current_user: dict = Depends(get_current_user)):
    """Get current live positions."""
    try:
        broker = get_broker()
        positions = await broker.get_positions()

        positions_data = []
        for pos in positions:
            if abs(pos.quantity) > 0:
                positions_data.append({
                    'symbol': pos.symbol,
                    'quantity': pos.quantity,
                    'average_price': pos.average_price,
                    'current_price': pos.current_price,
                    'pnl': pos.pnl,
                    'pnl_percent': pos.pnl_percent,
                    'product_type': pos.product_type.value,
                    'side': pos.side
                })

        return APIResponse(
            success=True,
            message="Live positions retrieved",
            data={
                "positions": positions_data,
                "total_positions": len(positions_data)
            }
        )

    except Exception as e:
        logger.error(f"Error getting live positions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get live positions")

@router.get("/orders", response_model=APIResponse)
async def get_live_orders(
    pagination: PaginationParams = Depends(),
    current_user: dict = Depends(get_current_user)
):
    """Get live orders."""
    try:
        all_orders = await order_manager.get_all_orders()

        # Combine all orders
        orders_data = []

        # Add pending orders
        for order in all_orders['pending']:
            if order:
                orders_data.append({
                    'order_id': getattr(order, 'order_id', 'pending'),
                    'symbol': order.symbol,
                    'side': order.side.value,
                    'quantity': order.quantity,
                    'order_type': order.order_type.value,
                    'price': order.price,
                    'status': order.status.value,
                    'timestamp': order.timestamp.isoformat() if order.timestamp else None
                })

        # Add active orders
        for order in all_orders['active']:
            orders_data.append({
                'order_id': order.broker_order_id or 'active',
                'symbol': order.symbol,
                'side': order.side.value,
                'quantity': order.quantity,
                'order_type': order.order_type.value,
                'price': order.price,
                'status': order.status.value,
                'filled_quantity': order.filled_quantity,
                'average_price': order.average_price,
                'timestamp': order.timestamp.isoformat() if order.timestamp else None
            })

        # Add completed orders
        for order in all_orders['completed']:
            orders_data.append({
                'order_id': order.broker_order_id or 'completed',
                'symbol': order.symbol,
                'side': order.side.value,
                'quantity': order.quantity,
                'order_type': order.order_type.value,
                'price': order.price,
                'status': order.status.value,
                'filled_quantity': order.filled_quantity,
                'average_price': order.average_price,
                'timestamp': order.timestamp.isoformat() if order.timestamp else None
            })

        # Apply pagination
        start_idx = pagination.offset
        end_idx = start_idx + pagination.size
        paginated_orders = orders_data[start_idx:end_idx]

        return APIResponse(
            success=True,
            message="Live orders retrieved",
            data={
                "orders": paginated_orders,
                "total": len(orders_data),
                "page": pagination.page,
                "size": pagination.size
            }
        )

    except Exception as e:
        logger.error(f"Error getting live orders: {e}")
        raise HTTPException(status_code=500, detail="Failed to get live orders")

@router.post("/orders", response_model=APIResponse)
async def place_manual_order(
    order_data: dict,
    current_user: dict = Depends(get_current_user)
):
    """Place manual order."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        # Validate required fields
        required_fields = ['symbol', 'side', 'quantity', 'order_type']
        for field in required_fields:
            if field not in order_data:
                raise HTTPException(status_code=400, detail=f"Missing required field: {field}")

        # Create order request
        order_request = OrderRequest(
            symbol=order_data['symbol'],
            side=OrderSide(order_data['side'].upper()),
            quantity=int(order_data['quantity']),
            order_type=OrderType(order_data['order_type'].upper()),
            price=order_data.get('price'),
            stop_price=order_data.get('stop_price'),
            product_type=ProductType(order_data.get('product_type', 'CNC').upper()),
            validity=order_data.get('validity', 'DAY'),
            metadata={'manual_order': True, 'user_id': current_user['id']}
        )

        # Submit order
        order_id = await order_manager.submit_order(order_request)

        return APIResponse(
            success=True,
            message="Manual order placed successfully",
            data={
                "order_id": order_id,
                "symbol": order_request.symbol,
                "side": order_request.side.value,
                "quantity": order_request.quantity
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error placing manual order: {e}")
        raise HTTPException(status_code=500, detail="Failed to place manual order")

@router.delete("/orders/{order_id}", response_model=APIResponse)
async def cancel_order(
    order_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Cancel order."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        success = await order_manager.cancel_order(order_id)

        if success:
            return APIResponse(
                success=True,
                message="Order cancelled successfully",
                data={"order_id": order_id, "cancelled": True}
            )
        else:
            return APIResponse(
                success=False,
                message="Failed to cancel order",
                data={"order_id": order_id, "cancelled": False}
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling order: {e}")
        raise HTTPException(status_code=500, detail="Failed to cancel order")

@router.get("/risk", response_model=APIResponse)
async def get_risk_status(current_user: dict = Depends(get_current_user)):
    """Get risk management status."""
    try:
        # Get portfolio risk
        portfolio_risk = await risk_manager.get_portfolio_risk()

        # Get risk violations
        risk_violations = await risk_manager.get_risk_violations()

        # Get position risks
        position_risks = await risk_manager.get_position_risks()

        risk_data = {
            'portfolio_risk': {
                'total_value': portfolio_risk.total_value if portfolio_risk else 0,
                'total_pnl': portfolio_risk.total_pnl if portfolio_risk else 0,
                'daily_pnl': portfolio_risk.daily_pnl if portfolio_risk else 0,
                'current_drawdown': portfolio_risk.current_drawdown if portfolio_risk else 0,
                'max_drawdown': portfolio_risk.max_drawdown if portfolio_risk else 0,
                'portfolio_var': portfolio_risk.portfolio_var if portfolio_risk else 0,
                'sharpe_ratio': portfolio_risk.sharpe_ratio if portfolio_risk else 0
            },
            'risk_violations': [
                {
                    'type': violation.violation_type.value,
                    'severity': violation.severity.value,
                    'symbol': violation.symbol,
                    'message': violation.message,
                    'timestamp': violation.timestamp.isoformat()
                }
                for violation in risk_violations
            ],
            'position_risks': {
                symbol: {
                    'position_size': risk.position_size,
                    'portfolio_weight': risk.portfolio_weight,
                    'volatility': risk.volatility,
                    'risk_score': risk.risk_score,
                    'sector': risk.sector
                }
                for symbol, risk in position_risks.items()
            }
        }

        return APIResponse(
            success=True,
            message="Risk status retrieved",
            data=risk_data
        )

    except Exception as e:
        logger.error(f"Error getting risk status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get risk status")

@router.post("/signals/generate", response_model=APIResponse)
async def generate_trading_signals(
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Generate trading signals for all strategies."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        # Generate signals in background
        background_tasks.add_task(_generate_signals_background)

        return APIResponse(
            success=True,
            message="Signal generation initiated",
            data={"status": "started"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating signals: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate signals")

async def _generate_signals_background():
    """Generate signals in background."""
    try:
        result = await live_trading_integration.generate_signals_for_all_strategies()

        # Send WebSocket notification
        await websocket_manager.broadcast({
            'type': 'signals_generated',
            'data': result
        }, 'signals')

        logger.info(f"Background signal generation completed: {result}")

    except Exception as e:
        logger.error(f"Error in background signal generation: {e}")

@router.get("/performance", response_model=APIResponse)
async def get_trading_performance(
    days: int = 30,
    current_user: dict = Depends(get_current_user)
):
    """Get trading performance metrics."""
    try:
        performance = await live_trading_integration.get_trading_performance(days)

        return APIResponse(
            success=True,
            message="Trading performance retrieved",
            data=performance
        )

    except Exception as e:
        logger.error(f"Error getting trading performance: {e}")
        raise HTTPException(status_code=500, detail="Failed to get trading performance")

@router.post("/emergency-stop", response_model=APIResponse)
async def emergency_stop(
    reason: str,
    current_user: dict = Depends(get_current_user)
):
    """Activate emergency stop."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        await risk_manager.activate_emergency_stop(reason)

        # Send WebSocket notification
        await websocket_manager.broadcast({
            'type': 'emergency_stop_activated',
            'data': {
                'reason': reason,
                'timestamp': datetime.now().isoformat(),
                'user': current_user['username']
            }
        }, 'alerts')

        return APIResponse(
            success=True,
            message="Emergency stop activated",
            data={
                "emergency_stop": True,
                "reason": reason,
                "timestamp": datetime.now().isoformat()
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error activating emergency stop: {e}")
        raise HTTPException(status_code=500, detail="Failed to activate emergency stop")

@router.post("/emergency-stop/deactivate", response_model=APIResponse)
async def deactivate_emergency_stop(current_user: dict = Depends(get_current_user)):
    """Deactivate emergency stop."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        await risk_manager.deactivate_emergency_stop()

        # Send WebSocket notification
        await websocket_manager.broadcast({
            'type': 'emergency_stop_deactivated',
            'data': {
                'timestamp': datetime.now().isoformat(),
                'user': current_user['username']
            }
        }, 'alerts')

        return APIResponse(
            success=True,
            message="Emergency stop deactivated",
            data={
                "emergency_stop": False,
                "timestamp": datetime.now().isoformat()
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deactivating emergency stop: {e}")
        raise HTTPException(status_code=500, detail="Failed to deactivate emergency stop")
