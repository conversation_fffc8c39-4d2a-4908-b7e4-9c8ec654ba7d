"""
Strategies Router - Handles trading strategies operations and monitoring.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import List, Optional, Dict, Any
from datetime import date, datetime, timedelta

from ..models import (
    APIResponse, StrategyConfig, StrategySignal, StrategyPerformance,
    PaginationParams
)
from ..auth import get_current_user
from ..websocket import websocket_manager
from ...strategies.strategy_registry import strategy_registry
from ...rl_optimizer.rl_integration import rl_integration
from ...data.database import db_manager
from ...data.crud import StockCRUD, SignalCRUD
from ...utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter()

@router.get("/", response_model=APIResponse)
async def get_strategies(current_user: dict = Depends(get_current_user)):
    """Get all available strategies."""
    try:
        strategies = strategy_registry.get_all_strategies()

        strategies_data = []
        for name, strategy_class in strategies.items():
            strategy_info = {
                'name': name,
                'description': getattr(strategy_class, 'description', 'No description available'),
                'parameters': getattr(strategy_class, 'default_parameters', {}),
                'enabled': strategy_registry.is_strategy_enabled(name),
                'last_signal': None,  # Would get from database
                'performance_metrics': {}  # Would calculate from historical data
            }
            strategies_data.append(strategy_info)

        return APIResponse(
            success=True,
            message="Strategies retrieved successfully",
            data={
                "strategies": strategies_data,
                "total": len(strategies_data)
            }
        )

    except Exception as e:
        logger.error(f"Error getting strategies: {e}")
        raise HTTPException(status_code=500, detail="Failed to get strategies")

@router.get("/{strategy_name}", response_model=APIResponse)
async def get_strategy_details(
    strategy_name: str,
    current_user: dict = Depends(get_current_user)
):
    """Get detailed strategy information."""
    try:
        strategy_class = strategy_registry.get_strategy(strategy_name)

        if not strategy_class:
            raise HTTPException(status_code=404, detail="Strategy not found")

        # Get strategy configuration
        config = strategy_registry.get_strategy_config(strategy_name)

        # Get recent signals
        with db_manager.get_session() as db:
            recent_signals = SignalCRUD.get_latest_signals(
                db, strategy_name=strategy_name, limit=10
            )

        signals_data = []
        for signal in recent_signals:
            signal_dict = {
                'id': signal.id,
                'symbol': signal.symbol,
                'signal_type': signal.signal_type,
                'signal_strength': float(signal.signal_strength),
                'price': float(signal.price),
                'target_price': float(signal.target_price) if signal.target_price else None,
                'stop_loss': float(signal.stop_loss) if signal.stop_loss else None,
                'confidence_score': float(signal.confidence_score),
                'signal_time': signal.signal_time.isoformat(),
                'is_valid': signal.is_valid
            }
            signals_data.append(signal_dict)

        strategy_details = {
            'name': strategy_name,
            'description': getattr(strategy_class, 'description', 'No description available'),
            'parameters': getattr(strategy_class, 'default_parameters', {}),
            'current_config': config,
            'enabled': strategy_registry.is_strategy_enabled(strategy_name),
            'recent_signals': signals_data,
            'signal_count': len(signals_data)
        }

        return APIResponse(
            success=True,
            message="Strategy details retrieved",
            data=strategy_details
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting strategy details: {e}")
        raise HTTPException(status_code=500, detail="Failed to get strategy details")

@router.post("/{strategy_name}/configure", response_model=APIResponse)
async def configure_strategy(
    strategy_name: str,
    config: StrategyConfig,
    current_user: dict = Depends(get_current_user)
):
    """Configure strategy parameters."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        # Validate strategy exists
        if not strategy_registry.get_strategy(strategy_name):
            raise HTTPException(status_code=404, detail="Strategy not found")

        # Update strategy configuration
        strategy_registry.update_strategy_config(strategy_name, {
            'enabled': config.enabled,
            'parameters': config.parameters
        })

        return APIResponse(
            success=True,
            message="Strategy configured successfully",
            data={
                "strategy_name": strategy_name,
                "enabled": config.enabled,
                "parameters": config.parameters
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error configuring strategy: {e}")
        raise HTTPException(status_code=500, detail="Failed to configure strategy")

@router.post("/{strategy_name}/enable", response_model=APIResponse)
async def enable_strategy(
    strategy_name: str,
    current_user: dict = Depends(get_current_user)
):
    """Enable strategy."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        success = strategy_registry.enable_strategy(strategy_name)

        if not success:
            raise HTTPException(status_code=404, detail="Strategy not found")

        return APIResponse(
            success=True,
            message=f"Strategy {strategy_name} enabled",
            data={"strategy_name": strategy_name, "enabled": True}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error enabling strategy: {e}")
        raise HTTPException(status_code=500, detail="Failed to enable strategy")

@router.post("/{strategy_name}/disable", response_model=APIResponse)
async def disable_strategy(
    strategy_name: str,
    current_user: dict = Depends(get_current_user)
):
    """Disable strategy."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        success = strategy_registry.disable_strategy(strategy_name)

        if not success:
            raise HTTPException(status_code=404, detail="Strategy not found")

        return APIResponse(
            success=True,
            message=f"Strategy {strategy_name} disabled",
            data={"strategy_name": strategy_name, "enabled": False}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error disabling strategy: {e}")
        raise HTTPException(status_code=500, detail="Failed to disable strategy")

@router.get("/{strategy_name}/signals", response_model=APIResponse)
async def get_strategy_signals(
    strategy_name: str,
    pagination: PaginationParams = Depends(),
    signal_type: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get strategy signals."""
    try:
        with db_manager.get_session() as db:
            # Build query filters
            query = db.query(StrategySignal).filter(
                StrategySignal.strategy_name == strategy_name,
                StrategySignal.is_valid == True
            )

            if signal_type:
                query = query.filter(StrategySignal.signal_type == signal_type)

            if start_date:
                query = query.filter(StrategySignal.signal_date >= start_date)

            if end_date:
                query = query.filter(StrategySignal.signal_date <= end_date)

            # Get total count
            total = query.count()

            # Apply pagination
            signals = query.order_by(StrategySignal.signal_time.desc()).offset(
                pagination.offset
            ).limit(pagination.size).all()

            signals_data = []
            for signal in signals:
                signal_dict = {
                    'id': signal.id,
                    'symbol': signal.symbol,
                    'signal_type': signal.signal_type,
                    'signal_strength': float(signal.signal_strength),
                    'price': float(signal.price),
                    'target_price': float(signal.target_price) if signal.target_price else None,
                    'stop_loss': float(signal.stop_loss) if signal.stop_loss else None,
                    'confidence_score': float(signal.confidence_score),
                    'signal_time': signal.signal_time.isoformat(),
                    'signal_date': signal.signal_date.isoformat(),
                    'parameters': signal.parameters
                }
                signals_data.append(signal_dict)

            return APIResponse(
                success=True,
                message="Strategy signals retrieved",
                data={
                    "signals": signals_data,
                    "total": total,
                    "page": pagination.page,
                    "size": pagination.size,
                    "pages": (total + pagination.size - 1) // pagination.size
                }
            )

    except Exception as e:
        logger.error(f"Error getting strategy signals: {e}")
        raise HTTPException(status_code=500, detail="Failed to get strategy signals")

@router.post("/{strategy_name}/generate-signals", response_model=APIResponse)
async def generate_signals(
    strategy_name: str,
    background_tasks: BackgroundTasks,
    symbols: Optional[List[str]] = None,
    current_user: dict = Depends(get_current_user)
):
    """Generate signals for strategy."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        # Validate strategy exists
        if not strategy_registry.get_strategy(strategy_name):
            raise HTTPException(status_code=404, detail="Strategy not found")

        # Generate signals in background
        background_tasks.add_task(
            _generate_strategy_signals,
            strategy_name,
            symbols or []
        )

        return APIResponse(
            success=True,
            message="Signal generation initiated",
            data={
                "strategy_name": strategy_name,
                "symbols": symbols or "all"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating signals: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate signals")

async def _generate_strategy_signals(strategy_name: str, symbols: List[str]):
    """Generate signals in background."""
    try:
        strategy = strategy_registry.create_strategy(strategy_name)

        if not symbols:
            # Get all Nifty 50 symbols
            with db_manager.get_session() as db:
                nifty50_stocks = StockCRUD.get_nifty50_stocks(db)
                symbols = [stock.symbol for stock in nifty50_stocks]

        # Get RL-optimized parameters if available
        optimized_params = rl_integration.get_optimized_parameters_for_strategy(
            strategy_name, symbols[0] if symbols else "RELIANCE"
        )

        if optimized_params:
            strategy.update_parameters(optimized_params)

        # Generate signals for each symbol
        generated_signals = []
        for symbol in symbols:
            try:
                signals = strategy.calculate_signals(symbol)
                generated_signals.extend(signals)

                # Send real-time update
                for signal in signals:
                    await websocket_manager.send_signal_update({
                        'strategy_name': strategy_name,
                        'symbol': symbol,
                        'signal': {
                            'type': signal.signal_type.value,
                            'strength': signal.signal_strength,
                            'price': signal.price,
                            'confidence': signal.confidence_score
                        }
                    })

            except Exception as e:
                logger.error(f"Error generating signals for {symbol}: {e}")

        logger.info(f"Generated {len(generated_signals)} signals for {strategy_name}")

    except Exception as e:
        logger.error(f"Error in background signal generation: {e}")

@router.get("/{strategy_name}/performance", response_model=APIResponse)
async def get_strategy_performance(
    strategy_name: str,
    days: int = 30,
    current_user: dict = Depends(get_current_user)
):
    """Get strategy performance metrics."""
    try:
        with db_manager.get_session() as db:
            # Get signals from last N days
            start_date = datetime.now() - timedelta(days=days)

            signals = db.query(StrategySignal).filter(
                StrategySignal.strategy_name == strategy_name,
                StrategySignal.signal_time >= start_date,
                StrategySignal.is_valid == True
            ).all()

            if not signals:
                return APIResponse(
                    success=True,
                    message="No signals found for performance calculation",
                    data={
                        "strategy_name": strategy_name,
                        "period_days": days,
                        "performance": None
                    }
                )

            # Calculate basic performance metrics
            total_signals = len(signals)
            buy_signals = len([s for s in signals if s.signal_type == 'BUY'])
            sell_signals = len([s for s in signals if s.signal_type == 'SELL'])

            # Calculate average confidence
            avg_confidence = sum(float(s.confidence_score) for s in signals) / total_signals
            avg_strength = sum(float(s.signal_strength) for s in signals) / total_signals

            # Get unique symbols
            unique_symbols = len(set(s.symbol for s in signals))

            performance = {
                'strategy_name': strategy_name,
                'period_days': days,
                'total_signals': total_signals,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'unique_symbols': unique_symbols,
                'avg_confidence': round(avg_confidence, 3),
                'avg_strength': round(avg_strength, 3),
                'signals_per_day': round(total_signals / days, 2),
                'last_signal': max(s.signal_time for s in signals).isoformat()
            }

            return APIResponse(
                success=True,
                message="Strategy performance retrieved",
                data=performance
            )

    except Exception as e:
        logger.error(f"Error getting strategy performance: {e}")
        raise HTTPException(status_code=500, detail="Failed to get strategy performance")

@router.get("/{strategy_name}/parameters", response_model=APIResponse)
async def get_strategy_parameters(
    strategy_name: str,
    symbol: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get strategy parameters (including RL-optimized)."""
    try:
        # Get default parameters
        strategy_class = strategy_registry.get_strategy(strategy_name)
        if not strategy_class:
            raise HTTPException(status_code=404, detail="Strategy not found")

        default_params = getattr(strategy_class, 'default_parameters', {})
        current_config = strategy_registry.get_strategy_config(strategy_name)

        # Get RL-optimized parameters if symbol provided
        rl_optimized_params = {}
        if symbol:
            rl_optimized_params = rl_integration.get_optimized_parameters_for_strategy(
                strategy_name, symbol
            )

        return APIResponse(
            success=True,
            message="Strategy parameters retrieved",
            data={
                "strategy_name": strategy_name,
                "symbol": symbol,
                "default_parameters": default_params,
                "current_config": current_config,
                "rl_optimized_parameters": rl_optimized_params
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting strategy parameters: {e}")
        raise HTTPException(status_code=500, detail="Failed to get strategy parameters")

@router.post("/optimize-all", response_model=APIResponse)
async def optimize_all_strategies(
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user)
):
    """Optimize all strategies using RL."""
    try:
        if not current_user.get('is_admin', False):
            raise HTTPException(status_code=403, detail="Admin access required")

        # Start RL training in background
        background_tasks.add_task(_run_rl_optimization)

        return APIResponse(
            success=True,
            message="Strategy optimization initiated",
            data={"status": "started"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting strategy optimization: {e}")
        raise HTTPException(status_code=500, detail="Failed to start optimization")

async def _run_rl_optimization():
    """Run RL optimization in background."""
    try:
        await rl_integration.manager.train_all_agents(num_iterations=30)
        logger.info("RL optimization completed")

        # Send WebSocket update
        await websocket_manager.send_rl_training_update({
            'status': 'completed',
            'message': 'All strategies optimized successfully'
        })

    except Exception as e:
        logger.error(f"Error in RL optimization: {e}")
        await websocket_manager.send_rl_training_update({
            'status': 'failed',
            'message': f'Optimization failed: {str(e)}'
        })
