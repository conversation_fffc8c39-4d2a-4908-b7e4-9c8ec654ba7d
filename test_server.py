#!/usr/bin/env python3
"""
Simple test server to verify FastAPI setup works.
"""

from fastapi import FastAPI
import uvicorn

app = FastAPI(title="Stock Analyzer Test API")

@app.get("/")
async def root():
    return {"message": "Stock Analyzer Test API is running!"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "message": "Test server is running",
        "version": "1.0.0"
    }

@app.get("/test")
async def test_endpoint():
    return {
        "test": "success",
        "endpoints": ["/", "/health", "/test"]
    }

if __name__ == "__main__":
    print("Starting test server on http://localhost:8000")
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
